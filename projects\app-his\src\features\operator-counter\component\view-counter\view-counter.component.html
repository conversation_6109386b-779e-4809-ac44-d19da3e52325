<!-- Clean Counter Operator Dashboard -->
<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto p-6">

    <!-- Clean Header Section -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
      <div class="flex items-center space-x-4">
        <div class="relative">
          <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center shadow-sm">
            <mat-icon class="text-white text-xl" svgIcon="mat_outline:price_change"></mat-icon>
          </div>
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
        </div>
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Counter Operator</h1>
          <p class="text-gray-600 text-sm mt-1">Counter management system</p>
          <div class="flex items-center space-x-2 mt-1">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-xs text-gray-500">System Online</span>
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <!-- Quick Stats -->
        <div class="hidden lg:flex items-center space-x-4 bg-white rounded-lg px-4 py-2 border border-gray-200 shadow-sm">
          <div class="text-center">
            <div class="text-lg font-semibold text-blue-600">{{loginCount}}</div>
            <div class="text-xs text-gray-500">Logins</div>
          </div>
          <div class="w-px h-6 bg-gray-300"></div>
          <div class="text-center">
            <div class="text-lg font-semibold text-green-600">{{sessionCount}}</div>
            <div class="text-xs text-gray-500">Sessions</div>
          </div>
        </div>

        <button
          class="flex items-center space-x-2 bg-white hover:bg-gray-50 text-gray-700 hover:text-blue-600 px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200"
          (click)="print()">
          <mat-icon class="text-blue-600 text-sm" svgIcon="mat_solid:print"></mat-icon>
          <span class="font-medium text-sm">Print Report</span>
        </button>
      </div>
    </div>

    <!-- Counter Information Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="bg-blue-600 px-6 py-4 rounded-t-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <mat-icon class="text-white text-lg" svgIcon="mat_outline:price_change"></mat-icon>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-white">Counter Information</h2>
              <p class="text-blue-100 text-sm">Real-time counter status</p>
            </div>
          </div>
          <div class="flex items-center space-x-2 bg-white/20 rounded-full px-3 py-1">
            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
            <span class="text-white text-sm font-medium">Live</span>
          </div>
        </div>
      </div>

      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Counter Details -->
          <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-800 mb-1">Counter Terminal</h3>
                <p class="text-xs text-gray-600">Active workstation</p>
              </div>
              <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:desktop_windows"></mat-icon>
              </div>
            </div>
            <div class="space-y-2">
              <p class="text-xl font-bold text-blue-700">{{counterDes}}</p>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span class="text-xs text-gray-600">Terminal ID: {{counterDes}}</span>
              </div>
            </div>
          </div>

          <!-- Date -->
          <div class="bg-green-50 rounded-lg p-4 border border-green-100">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-800 mb-1">Current Date</h3>
                <p class="text-xs text-gray-600">System timestamp</p>
              </div>
              <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:calendar_today"></mat-icon>
              </div>
            </div>
            <div class="space-y-2">
              <p class="text-xl font-bold text-green-700">{{todayDate | date: "dd-MM-yyyy"}}</p>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span class="text-xs text-gray-600">{{todayDate | date: "EEEE"}}</span>
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="bg-purple-50 rounded-lg p-4 border border-purple-100">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-800 mb-1">Counter Status</h3>
                <p class="text-xs text-gray-600">Operational state</p>
              </div>
              <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:info"></mat-icon>
              </div>
            </div>
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="relative">
                  <div class="w-3 h-3 rounded-full"
                       [ngClass]="counterStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                </div>
                <p class="text-xl font-bold"
                   [ngClass]="counterStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{counterStatus}}</p>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span class="text-xs text-gray-600">Updated: {{todayDate | date: "HH:mm"}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Session Information Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="bg-indigo-600 px-6 py-4 rounded-t-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <mat-icon class="text-white text-lg" svgIcon="mat_outline:people"></mat-icon>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-white">Session Management</h2>
              <p class="text-indigo-100 text-sm">Active session monitoring</p>
            </div>
          </div>
          <div class="bg-white/20 rounded-full px-3 py-1">
            <span class="text-white text-sm font-medium">{{sessionCount}} Active</span>
          </div>
        </div>
      </div>

      <div class="p-6">
        <!-- Main Session Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <!-- Login Count -->
          <div class="bg-orange-50 rounded-lg p-4 border border-orange-100">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-800 mb-1">Login Sessions</h3>
                <p class="text-xs text-gray-600">Total user logins</p>
              </div>
              <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:login"></mat-icon>
              </div>
            </div>
            <div class="space-y-2">
              <p class="text-2xl font-bold text-orange-700">{{loginCount}}</p>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span class="text-xs text-gray-600">Active sessions</span>
              </div>
            </div>
          </div>

          <!-- Session Count -->
          <div class="bg-cyan-50 rounded-lg p-4 border border-cyan-100">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-800 mb-1">Session Count</h3>
                <p class="text-xs text-gray-600">Total sessions today</p>
              </div>
              <div class="w-8 h-8 bg-cyan-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:schedule"></mat-icon>
              </div>
            </div>
            <div class="space-y-2">
              <p class="text-2xl font-bold text-cyan-700">{{sessionCount}}</p>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-cyan-500 rounded-full"></div>
                <span class="text-xs text-gray-600">Running sessions</span>
              </div>
            </div>
          </div>

          <!-- Session Status -->
          <div class="bg-rose-50 rounded-lg p-4 border border-rose-100">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-800 mb-1">Session Status</h3>
                <p class="text-xs text-gray-600">Current state</p>
              </div>
              <div class="w-8 h-8 bg-rose-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:info"></mat-icon>
              </div>
            </div>
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="relative">
                  <div class="w-3 h-3 rounded-full"
                       [ngClass]="sessionStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                </div>
                <p class="text-xl font-bold"
                   [ngClass]="sessionStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{sessionStatus}}</p>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-rose-500 rounded-full"></div>
                <span class="text-xs text-gray-600">Real-time status</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Time and Financial Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Start Time -->
          <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:schedule"></mat-icon>
              </div>
              <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">START</div>
            </div>
            <h4 class="text-xs font-semibold text-gray-700 mb-2">Session Start</h4>
            <p class="text-lg font-bold text-gray-700">{{startTime || 'Not Started'}}</p>
          </div>

          <!-- End Time -->
          <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:schedule"></mat-icon>
              </div>
              <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">END</div>
            </div>
            <h4 class="text-xs font-semibold text-gray-700 mb-2">Session End</h4>
            <p class="text-lg font-bold text-gray-700">{{endTime || 'Active'}}</p>
          </div>

          <!-- Float Amount -->
          <div class="bg-emerald-50 rounded-lg p-4 border border-emerald-100">
            <div class="flex items-center justify-between mb-3">
              <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:attach_money"></mat-icon>
              </div>
              <div class="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">FLOAT</div>
            </div>
            <h4 class="text-xs font-semibold text-gray-700 mb-2">Float Amount</h4>
            <p class="text-lg font-bold text-emerald-700">${{floatAmount || '0.00'}}</p>
          </div>

          <!-- Opening Cash -->
          <div class="bg-emerald-50 rounded-lg p-4 border border-emerald-100">
            <div class="flex items-center justify-between mb-3">
              <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-sm" svgIcon="mat_outline:account_balance_wallet"></mat-icon>
              </div>
              <div class="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">CASH</div>
            </div>
            <h4 class="text-xs font-semibold text-gray-700 mb-2">Opening Cash</h4>
            <p class="text-lg font-bold text-emerald-700">${{openingAmount || '0.00'}}</p>
          </div>
        </div>

        <!-- User Information -->
        <div class="mt-6 bg-indigo-50 rounded-lg p-4 border border-indigo-100">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                <mat-icon class="text-white text-lg" svgIcon="mat_outline:person"></mat-icon>
              </div>
              <div>
                <h4 class="text-xs font-semibold text-gray-600 mb-1">Current Operator</h4>
                <p class="text-lg font-bold text-indigo-700">{{counterDes}}</p>
              </div>
            </div>
            <div class="hidden md:flex items-center space-x-4">
              <div class="text-center">
                <div class="text-xs text-gray-600">Session Duration</div>
                <div class="text-sm font-bold text-gray-800">{{startTime ? 'Active' : 'Inactive'}}</div>
              </div>
              <div class="w-px h-8 bg-gray-300"></div>
              <div class="text-center">
                <div class="text-xs text-gray-600">Access Level</div>
                <div class="text-sm font-bold text-indigo-600">Operator</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Control Center -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="text-center mb-6">
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Control Center</h3>
        <p class="text-gray-600 text-sm">Manage counter operations and sessions</p>
      </div>

      <div class="flex flex-col lg:flex-row gap-4 justify-center items-center">
        <!-- Counter Control Button -->
        <button
          class="flex items-center space-x-3 px-6 py-3 rounded-lg font-semibold text-sm transition-all duration-200 min-w-[180px] shadow-sm hover:shadow-md"
          [ngClass]="{
            'bg-blue-600 hover:bg-blue-700 text-white': !disableCounter,
            'bg-gray-300 text-gray-500 cursor-not-allowed': disableCounter
          }"
          [disabled]="disableCounter"
          (click)="openCounter()">
          <div class="w-5 h-5 flex items-center justify-center">
            <mat-icon class="text-current text-lg" svgIcon="mat_outline:lock_open"></mat-icon>
          </div>
          <div class="text-left">
            <div class="font-semibold">{{counterButton}}</div>
            <div class="text-xs opacity-80">Counter Operations</div>
          </div>
        </button>

        <!-- Session Control Button -->
        <button
          class="flex items-center space-x-3 px-6 py-3 rounded-lg font-semibold text-sm transition-all duration-200 min-w-[180px] shadow-sm hover:shadow-md"
          [ngClass]="{
            'bg-green-600 hover:bg-green-700 text-white': !disableSession,
            'bg-gray-300 text-gray-500 cursor-not-allowed': disableSession
          }"
          [disabled]="disableSession"
          (click)="session()">
          <div class="w-5 h-5 flex items-center justify-center">
            <mat-icon class="text-current text-lg" svgIcon="mat_outline:play_arrow"></mat-icon>
          </div>
          <div class="text-left">
            <div class="font-semibold">{{sessionButton}}</div>
            <div class="text-xs opacity-80">Session Management</div>
          </div>
        </button>

        <!-- Confirm Actuals Button -->
        <button
          class="flex items-center space-x-3 px-6 py-3 rounded-lg font-semibold text-sm transition-all duration-200 min-w-[180px] shadow-sm hover:shadow-md"
          [ngClass]="{
            'bg-purple-600 hover:bg-purple-700 text-white': !disableConfirmActual,
            'bg-gray-300 text-gray-500 cursor-not-allowed': disableConfirmActual
          }"
          [disabled]="disableConfirmActual"
          (click)="confirmActuals()">
          <div class="w-5 h-5 flex items-center justify-center">
            <mat-icon class="text-current text-lg" svgIcon="mat_outline:check_circle"></mat-icon>
          </div>
          <div class="text-left">
            <div class="font-semibold">Confirm Actuals</div>
            <div class="text-xs opacity-80">Financial Verification</div>
          </div>
        </button>
      </div>

      <!-- Quick Status Bar -->
      <div class="mt-6 flex items-center justify-center space-x-6 text-sm text-gray-600">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span>Counter: {{counterStatus}}</span>
        </div>
        <div class="w-px h-4 bg-gray-300"></div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Session: {{sessionStatus}}</span>
        </div>
        <div class="w-px h-4 bg-gray-300"></div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
          <span>System: Online</span>
        </div>
      </div>
    </div>
  </div>
</div>
