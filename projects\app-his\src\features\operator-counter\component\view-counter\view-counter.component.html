<!-- Ultra Modern Counter Operator Dashboard -->
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
  <div class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"></div>

  <div class="relative z-10 max-w-7xl mx-auto p-6">

    <!-- Enhanced Header Section -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-10 space-y-4 lg:space-y-0">
      <div class="flex items-center space-x-4">
        <div class="relative">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl flex items-center justify-center shadow-xl">
            <mat-icon class="text-white text-3xl" svgIcon="mat_outline:price_change"></mat-icon>
          </div>
          <div class="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-4 border-white pulse-animation"></div>
        </div>
        <div>
          <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
            Counter Operator
          </h1>
          <p class="text-gray-600 text-lg mt-1">Advanced counter management system</p>
          <div class="flex items-center space-x-2 mt-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm text-gray-500">System Online</span>
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <!-- Quick Stats -->
        <div class="hidden lg:flex items-center space-x-6 bg-white/80 backdrop-blur-sm rounded-2xl px-6 py-3 border border-gray-200/50">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{loginCount}}</div>
            <div class="text-xs text-gray-500">Logins</div>
          </div>
          <div class="w-px h-8 bg-gray-300"></div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{sessionCount}}</div>
            <div class="text-xs text-gray-500">Sessions</div>
          </div>
        </div>

        <button
          class="group flex items-center space-x-3 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-700 hover:text-blue-600 px-6 py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200/50 hover:border-blue-300"
          (click)="print()">
          <mat-icon class="text-blue-600 group-hover:scale-110 transition-transform duration-200" svgIcon="mat_solid:print"></mat-icon>
          <span class="font-semibold">Print Report</span>
        </button>
      </div>
    </div>

    <!-- Enhanced Counter Information Card -->
    <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 mb-10 overflow-hidden hover:shadow-3xl transition-all duration-500">
      <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 px-8 py-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90"></div>
        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
        <div class="relative z-10 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
              <mat-icon class="text-white text-2xl" svgIcon="mat_outline:price_change"></mat-icon>
            </div>
            <div>
              <h2 class="text-3xl font-bold text-white">Counter Information</h2>
              <p class="text-blue-100">Real-time counter status and details</p>
            </div>
          </div>
          <div class="hidden md:flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-white text-sm font-medium">Live</span>
          </div>
        </div>
      </div>

      <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Counter Details -->
          <div class="group/card relative bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-100 rounded-2xl p-6 border border-blue-200/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h3 class="text-lg font-bold text-gray-800 mb-1">Counter Terminal</h3>
                  <p class="text-sm text-gray-600">Active workstation</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg group-hover/card:scale-110 transition-transform duration-300">
                  <mat-icon class="text-white text-xl" svgIcon="mat_outline:desktop_windows"></mat-icon>
                </div>
              </div>
              <div class="space-y-2">
                <p class="text-3xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">{{counterDes}}</p>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">Terminal ID: {{counterDes}}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Date -->
          <div class="group/card relative bg-gradient-to-br from-emerald-50 via-green-50 to-teal-100 rounded-2xl p-6 border border-emerald-200/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-500/5 opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h3 class="text-lg font-bold text-gray-800 mb-1">Current Date</h3>
                  <p class="text-sm text-gray-600">System timestamp</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg group-hover/card:scale-110 transition-transform duration-300">
                  <mat-icon class="text-white text-xl" svgIcon="mat_outline:calendar_today"></mat-icon>
                </div>
              </div>
              <div class="space-y-2">
                <p class="text-3xl font-bold bg-gradient-to-r from-emerald-700 to-teal-700 bg-clip-text text-transparent">{{todayDate | date: "dd-MM-yyyy"}}</p>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">{{todayDate | date: "EEEE"}}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="group/card relative bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100 rounded-2xl p-6 border border-purple-200/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-violet-500/5 opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h3 class="text-lg font-bold text-gray-800 mb-1">Counter Status</h3>
                  <p class="text-sm text-gray-600">Operational state</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg group-hover/card:scale-110 transition-transform duration-300">
                  <mat-icon class="text-white text-xl" svgIcon="mat_outline:info"></mat-icon>
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <div class="w-4 h-4 rounded-full"
                         [ngClass]="counterStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                    <div class="absolute inset-0 w-4 h-4 rounded-full animate-ping"
                         [ngClass]="counterStatus === 'Open' ? 'bg-green-400' : 'bg-red-400'"></div>
                  </div>
                  <p class="text-3xl font-bold"
                     [ngClass]="counterStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{counterStatus}}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">Last updated: {{todayDate | date: "HH:mm"}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Session Information Card -->
    <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 mb-10 overflow-hidden hover:shadow-3xl transition-all duration-500">
      <div class="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 px-8 py-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-purple-600/90"></div>
        <div class="absolute top-0 left-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 -translate-x-20"></div>
        <div class="absolute bottom-0 right-0 w-32 h-32 bg-white/10 rounded-full translate-y-16 translate-x-16"></div>
        <div class="relative z-10 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
              <mat-icon class="text-white text-2xl" svgIcon="mat_outline:people"></mat-icon>
            </div>
            <div>
              <h2 class="text-3xl font-bold text-white">Session Management</h2>
              <p class="text-purple-100">Active session monitoring and control</p>
            </div>
          </div>
          <div class="hidden md:flex items-center space-x-3">
            <div class="bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm">
              <span class="text-white text-sm font-medium">{{sessionCount}} Active</span>
            </div>
          </div>
        </div>
      </div>

      <div class="p-8">
        <!-- Main Session Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Login Count -->
          <div class="group/metric relative bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-100 rounded-2xl p-6 border border-orange-200/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-amber-500/5 opacity-0 group-hover/metric:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h3 class="text-lg font-bold text-gray-800 mb-1">Login Sessions</h3>
                  <p class="text-sm text-gray-600">Total user logins</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg group-hover/metric:scale-110 transition-transform duration-300">
                  <mat-icon class="text-white text-xl" svgIcon="mat_outline:login"></mat-icon>
                </div>
              </div>
              <div class="space-y-2">
                <p class="text-4xl font-bold bg-gradient-to-r from-orange-700 to-amber-700 bg-clip-text text-transparent">{{loginCount}}</p>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  <span class="text-sm text-gray-600">Active sessions</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Session Count -->
          <div class="group/metric relative bg-gradient-to-br from-cyan-50 via-blue-50 to-indigo-100 rounded-2xl p-6 border border-cyan-200/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-blue-500/5 opacity-0 group-hover/metric:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h3 class="text-lg font-bold text-gray-800 mb-1">Session Count</h3>
                  <p class="text-sm text-gray-600">Total sessions today</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover/metric:scale-110 transition-transform duration-300">
                  <mat-icon class="text-white text-xl" svgIcon="mat_outline:schedule"></mat-icon>
                </div>
              </div>
              <div class="space-y-2">
                <p class="text-4xl font-bold bg-gradient-to-r from-cyan-700 to-blue-700 bg-clip-text text-transparent">{{sessionCount}}</p>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></div>
                  <span class="text-sm text-gray-600">Running sessions</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Session Status -->
          <div class="group/metric relative bg-gradient-to-br from-rose-50 via-pink-50 to-purple-100 rounded-2xl p-6 border border-rose-200/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-rose-500/5 to-pink-500/5 opacity-0 group-hover/metric:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h3 class="text-lg font-bold text-gray-800 mb-1">Session Status</h3>
                  <p class="text-sm text-gray-600">Current state</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-rose-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg group-hover/metric:scale-110 transition-transform duration-300">
                  <mat-icon class="text-white text-xl" svgIcon="mat_outline:info"></mat-icon>
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <div class="w-4 h-4 rounded-full"
                         [ngClass]="sessionStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                    <div class="absolute inset-0 w-4 h-4 rounded-full animate-ping"
                         [ngClass]="sessionStatus === 'Open' ? 'bg-green-400' : 'bg-red-400'"></div>
                  </div>
                  <p class="text-3xl font-bold"
                     [ngClass]="sessionStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{sessionStatus}}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-rose-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">Real-time status</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Time and Financial Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Start Time -->
          <div class="group/time relative bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl p-6 border border-slate-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5 opacity-0 group-hover/time:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-4">
                <div class="w-10 h-10 bg-gradient-to-br from-slate-500 to-gray-600 rounded-lg flex items-center justify-center">
                  <mat-icon class="text-white text-sm" svgIcon="mat_outline:schedule"></mat-icon>
                </div>
                <div class="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">START</div>
              </div>
              <h4 class="text-sm font-semibold text-gray-700 mb-2">Session Start</h4>
              <p class="text-xl font-bold text-slate-700">{{startTime || 'Not Started'}}</p>
            </div>
          </div>

          <!-- End Time -->
          <div class="group/time relative bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl p-6 border border-slate-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5 opacity-0 group-hover/time:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-4">
                <div class="w-10 h-10 bg-gradient-to-br from-slate-500 to-gray-600 rounded-lg flex items-center justify-center">
                  <mat-icon class="text-white text-sm" svgIcon="mat_outline:schedule"></mat-icon>
                </div>
                <div class="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">END</div>
              </div>
              <h4 class="text-sm font-semibold text-gray-700 mb-2">Session End</h4>
              <p class="text-xl font-bold text-slate-700">{{endTime || 'Active'}}</p>
            </div>
          </div>

          <!-- Float Amount -->
          <div class="group/money relative bg-gradient-to-br from-emerald-50 to-green-100 rounded-2xl p-6 border border-emerald-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 opacity-0 group-hover/money:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-4">
                <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
                  <mat-icon class="text-white text-sm" svgIcon="mat_outline:attach_money"></mat-icon>
                </div>
                <div class="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">FLOAT</div>
              </div>
              <h4 class="text-sm font-semibold text-gray-700 mb-2">Float Amount</h4>
              <p class="text-xl font-bold text-emerald-700">${{floatAmount || '0.00'}}</p>
            </div>
          </div>

          <!-- Opening Cash -->
          <div class="group/money relative bg-gradient-to-br from-emerald-50 to-green-100 rounded-2xl p-6 border border-emerald-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 opacity-0 group-hover/money:opacity-100 transition-opacity duration-300"></div>
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-4">
                <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
                  <mat-icon class="text-white text-sm" svgIcon="mat_outline:account_balance_wallet"></mat-icon>
                </div>
                <div class="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">CASH</div>
              </div>
              <h4 class="text-sm font-semibold text-gray-700 mb-2">Opening Cash</h4>
              <p class="text-xl font-bold text-emerald-700">${{openingAmount || '0.00'}}</p>
            </div>
          </div>
        </div>

        <!-- Enhanced User Information -->
        <div class="mt-8 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 rounded-2xl p-6 border border-indigo-200/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <mat-icon class="text-white text-xl" svgIcon="mat_outline:person"></mat-icon>
              </div>
              <div>
                <h4 class="text-sm font-semibold text-gray-600 mb-1">Current Operator</h4>
                <p class="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent">{{counterDes}}</p>
              </div>
            </div>
            <div class="hidden md:flex items-center space-x-4">
              <div class="text-center">
                <div class="text-sm text-gray-600">Session Duration</div>
                <div class="text-lg font-bold text-gray-800">{{startTime ? 'Active' : 'Inactive'}}</div>
              </div>
              <div class="w-px h-12 bg-gray-300"></div>
              <div class="text-center">
                <div class="text-sm text-gray-600">Access Level</div>
                <div class="text-lg font-bold text-indigo-600">Operator</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Action Control Center -->
    <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 p-8 overflow-hidden">
      <div class="text-center mb-8">
        <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
          Control Center
        </h3>
        <p class="text-gray-600">Manage counter operations and sessions</p>
      </div>

      <div class="flex flex-col lg:flex-row gap-6 justify-center items-center">
        <!-- Counter Control Button -->
        <button
          class="group relative flex items-center space-x-4 px-10 py-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 min-w-[200px] overflow-hidden"
          [ngClass]="{
            'bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 text-white': !disableCounter,
            'bg-gray-300 text-gray-500 cursor-not-allowed': disableCounter
          }"
          [disabled]="disableCounter"
          (click)="openCounter()">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10 flex items-center space-x-4">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <mat-icon class="text-white" svgIcon="mat_outline:lock_open"></mat-icon>
            </div>
            <div class="text-left">
              <div class="text-lg font-bold">{{counterButton}}</div>
              <div class="text-sm opacity-80">Counter Operations</div>
            </div>
          </div>
          <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
        </button>

        <!-- Session Control Button -->
        <button
          class="group relative flex items-center space-x-4 px-10 py-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 min-w-[200px] overflow-hidden"
          [ngClass]="{
            'bg-gradient-to-r from-emerald-600 via-green-700 to-teal-700 hover:from-emerald-700 hover:via-green-800 hover:to-teal-800 text-white': !disableSession,
            'bg-gray-300 text-gray-500 cursor-not-allowed': disableSession
          }"
          [disabled]="disableSession"
          (click)="session()">
          <div class="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10 flex items-center space-x-4">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <mat-icon class="text-white" svgIcon="mat_outline:play_arrow"></mat-icon>
            </div>
            <div class="text-left">
              <div class="text-lg font-bold">{{sessionButton}}</div>
              <div class="text-sm opacity-80">Session Management</div>
            </div>
          </div>
          <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
        </button>

        <!-- Confirm Actuals Button -->
        <button
          class="group relative flex items-center space-x-4 px-10 py-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 min-w-[200px] overflow-hidden"
          [ngClass]="{
            'bg-gradient-to-r from-purple-600 via-violet-700 to-purple-700 hover:from-purple-700 hover:via-violet-800 hover:to-purple-800 text-white': !disableConfirmActual,
            'bg-gray-300 text-gray-500 cursor-not-allowed': disableConfirmActual
          }"
          [disabled]="disableConfirmActual"
          (click)="confirmActuals()">
          <div class="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-violet-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10 flex items-center space-x-4">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <mat-icon class="text-white" svgIcon="mat_outline:check_circle"></mat-icon>
            </div>
            <div class="text-left">
              <div class="text-lg font-bold">Confirm Actuals</div>
              <div class="text-sm opacity-80">Financial Verification</div>
            </div>
          </div>
          <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
        </button>
      </div>

      <!-- Quick Status Bar -->
      <div class="mt-8 flex items-center justify-center space-x-8 text-sm text-gray-600">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span>Counter: {{counterStatus}}</span>
        </div>
        <div class="w-px h-4 bg-gray-300"></div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Session: {{sessionStatus}}</span>
        </div>
        <div class="w-px h-4 bg-gray-300"></div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          <span>System: Online</span>
        </div>
      </div>
    </div>
  </div>
</div>
