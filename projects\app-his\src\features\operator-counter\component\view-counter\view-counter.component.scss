/* Ultra Modern Counter Operator Styles */

/* Advanced animations and transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Background pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Component specific animations */
.counter-card {
  animation: fadeInUp 0.8s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Enhanced button effects */
button:not(:disabled) {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:not(:disabled):hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1);
}

button:not(:disabled):active {
  transform: translateY(-2px) scale(1.01);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Advanced card hover effects */
.group:hover {
  transform: translateY(-4px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.group\/card:hover {
  transform: translateY(-2px) scale(1.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.group\/metric:hover {
  transform: translateY(-3px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.group\/time:hover,
.group\/money:hover {
  transform: translateY(-2px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Shimmer effect for loading states */
.shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Status indicator animations */
.status-online {
  position: relative;
}

.status-online::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
  opacity: 0.6;
}

/* Print styles */
@media print {
  body * {
    visibility: hidden;
  }

  .print-section, .print-section * {
    visibility: visible;
  }

  .print-section {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0;
    margin: 0;
  }

  /* Hide action buttons in print */
  button {
    display: none !important;
  }

  /* Improve print layout */
  .bg-white {
    background-color: white !important;
    box-shadow: none !important;
  }

  /* Remove gradients for print */
  .bg-gradient-to-r,
  .bg-gradient-to-br {
    background: white !important;
  }
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .flex-wrap {
    flex-direction: column;
  }

  button {
    width: 100%;
    justify-content: center;
    min-width: unset;
  }

  .lg\\:flex-row {
    flex-direction: column;
  }

  .space-x-4 > * + * {
    margin-left: 0;
    margin-top: 1rem;
  }

  .text-4xl {
    font-size: 2rem;
  }

  .text-3xl {
    font-size: 1.875rem;
  }

  .px-10 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (max-width: 480px) {
  .p-6 {
    padding: 1rem;
  }

  .p-8 {
    padding: 1.5rem;
  }

  .gap-8 {
    gap: 1rem;
  }

  .gap-6 {
    gap: 0.75rem;
  }
}

/* Enhanced custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(to bottom, #f1f5f9, #e2e8f0);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #cbd5e1, #94a3b8);
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #94a3b8, #64748b);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.group:focus-within {
  ring: 2px;
  ring-color: #3b82f6;
  ring-opacity: 0.5;
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .bg-white\\/80 {
    background-color: rgba(30, 41, 59, 0.8);
  }

  .text-gray-900 {
    color: #f1f5f9;
  }

  .text-gray-800 {
    color: #e2e8f0;
  }

  .text-gray-700 {
    color: #cbd5e1;
  }

  .text-gray-600 {
    color: #94a3b8;
  }

  .border-gray-200\\/50 {
    border-color: rgba(71, 85, 105, 0.5);
  }
}

/* Performance optimizations */
.group,
.group\\/card,
.group\\/metric,
.group\\/time,
.group\\/money {
  will-change: transform;
}

button {
  will-change: transform, box-shadow;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}