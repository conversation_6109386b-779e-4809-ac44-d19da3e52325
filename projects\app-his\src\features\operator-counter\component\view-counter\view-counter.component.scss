/* Clean Counter Operator Styles */

/* Simple button effects */
button:not(:disabled) {
  transition: all 0.2s ease-in-out;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:not(:disabled):active {
  transform: translateY(0);
  transition: all 0.1s ease-in-out;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Card hover effects */
.bg-white:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

/* Print styles */
@media print {
  body * {
    visibility: hidden;
  }

  .print-section, .print-section * {
    visibility: visible;
  }

  .print-section {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0;
    margin: 0;
  }

  /* Hide action buttons in print */
  button {
    display: none !important;
  }

  /* Improve print layout */
  .bg-white {
    background-color: white !important;
    box-shadow: none !important;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  button {
    width: 100%;
    justify-content: center;
    min-width: unset;
  }

  .lg\\:flex-row {
    flex-direction: column;
  }

  .space-x-4 > * + * {
    margin-left: 0;
    margin-top: 1rem;
  }
}

@media (max-width: 480px) {
  .p-6 {
    padding: 1rem;
  }

  .gap-6 {
    gap: 0.75rem;
  }

  .gap-4 {
    gap: 0.5rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}